import React from 'react';
import ReactD<PERSON> from 'react-dom/client';
import App from './App.jsx';
import Home from './routes/Home/Home.jsx';
import ForgotPassword from './routes/Forgot-password/Forgot-password.jsx';
import ResetPassword from './routes/Reset-password/Reset-password.jsx';
import PasswordChange from './routes/Password-change/Password-change.jsx';
import 'bootstrap/dist/css/bootstrap.min.css';
import { createBrowserRouter, RouterProvider } from 'react-router-dom';
import { AuthProvider } from './context/AuthProvider.jsx';


const router = createBrowserRouter([
  {
    path: '/',
    element: <App/>,
  },
  {
    path: 'home',
    element: <Home/>
  },
  {
    path: 'forgot-password',
    element: <ForgotPassword/>
  },
  {
    path: 'reset-password',
    element: <ResetPassword/>
  },
  {
    path: 'reset-password/:uid/:token',
    element: <ResetPassword/>
  },
  {
    path: 'password-change',
    element: <PasswordChange/>
  },
]);

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <AuthProvider>
      <RouterProvider router={router}/>
    </AuthProvider>
  </React.StrictMode>
);
