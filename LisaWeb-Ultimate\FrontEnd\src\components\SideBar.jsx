//icons
import { FaFile } from "react-icons/fa";
import { IoIosLogOut } from "react-icons/io";
import { FaTrash } from "react-icons/fa";
//css and bootstrap
import styles from './SideBar.module.css';
//react and react router
import { useState, useEffect, useContext  , useRef} from 'react';
import { useNavigate } from 'react-router-dom';
//axios
import axios from '../api/axios';
//context
import { AuthContext } from '../context/AuthProvider.jsx';
import { TailSpin } from 'react-loader-spinner';
import { FaHandPaper } from "react-icons/fa";


function SideBar({ setAlgoritmo, setExecutarSegmentacao }) {
    const [imageLinks, setImageLinks] = useState([]);
    const backendUrl = '/api/v1/exams/';
    const { authData, setAuthData } = useContext(AuthContext);
    const navigate = useNavigate();
    const [loading, setLoading] = useState(false);
    const fileInputRef = useRef(null);
    const redirecionarSeNaoLogado = () => {
        const storedAuthData = JSON.parse(localStorage.getItem('authData'));

        if (authData === null && (storedAuthData === undefined || storedAuthData === null)) {
            navigate("/");
        } else if (authData === null || authData === undefined) {
            setAuthData(storedAuthData);
        }
    }
    useEffect(() => {
        if (fileInputRef.current) {
          fileInputRef.current.setAttribute('webkitdirectory', '');
          fileInputRef.current.setAttribute('directory', '');
        }
      }, []);
    useEffect(() => {
        redirecionarSeNaoLogado();
        ReceberExames();
    }, [authData]);

    //enviar dicoms para o database
    const handleArquivos = async (e) => {
        redirecionarSeNaoLogado();
        const selectedFiles = e.target.files;
        if (authData != null) {
            //ativar spinner
            setLoading(true);

            //passar os dicoms para o formato reconhecido no backend
            const formData = new FormData();
            for (let i = 0; i < selectedFiles.length; i++) {
                formData.append('files[]', selectedFiles[i]);
            }
            //montando a requisição e enviando
            const requisition = {
                headers: {
                    'Content-Type': 'multipart/form-data',
                    'Authorization': `Bearer ${authData.access}`
                },
            };
            try {
                const response = await axios.post(backendUrl, formData, requisition);
                alert("Arquivo enviado com sucesso!");
                //atualizar os exames disponiveis
                ReceberExames();
            } catch (err) {
                alert("nâo foi possivel enviar os exames.");
            } finally {
                setLoading(false); // Desativa o spinner
            }
        }
    }
    /*
        receber os exames que já estão no database
        só é acionado quando entra na home ou o usuário envia dicoms 
        então não precisa checar se está logado
    */
    
    const ReceberExames = async () => {
        setImageLinks([]);
        if (authData?.access) {
            const requisition = {
                headers: {
                    'content-type': 'application/json',
                    'Authorization': `Bearer ${authData.access}`
                },
            };
            try {
                const responseExams = await axios.get(backendUrl, requisition);
                for (let patient of responseExams.data) {

                    const newObj = {
                        name: patient.patient_name,
                        series: []
                    }

                    for (let study of patient.studies) {
                        for (let serie of study.series) {
                            const arraySerieMember = {
                                scans: serie.ct_scans,
                                serieDescription: serie.series_description,
                                patientName: patient.patient_name,
                                patientAge: patient.patient_age,
                                patientSex: patient.patient_sex,
                                patientDicomId: patient.patient_dicom_id,
                                patientId: patient.patient_id,
                                serieData: study.study_date,
                                serieManufatura: study.manufacturer,
                                seriesID: serie.series_instance_uid
                            };
                            newObj.series.push(arraySerieMember);
                        }
                    }

                    setImageLinks(s => [...s, newObj]);
                }

            } catch (err) {
                localStorage.setItem('authData', JSON.stringify(null));
                setAuthData(null);
                console.log(err);
            }
        } else {
            console.log("chave de acesso não definida");
        }

    }

    const logout = () => {
        setAuthData(null);
        localStorage.setItem('authData', JSON.stringify(null));
        navigate("/");
    }

    const deleteExam = async (patientID) => {
        if (authData?.access) {
            const deleteUrl = `/api/v1/patients/delete/${patientID}/`;  // A URL não precisa mais do patientID como parte da rota
            const config = {
                headers: {
                    'Authorization': `Bearer ${authData.access}`,
                    'Content-Type': 'application/json',
                },
            };

            try {
                // Fazer requisição DELETE com o patientID no corpo
                await axios.delete(deleteUrl, config);
                alert('Exames deletado com sucesso');

                // Atualizar lista de exames após deletar
                ReceberExames();
            } catch (error) {
                console.error('Erro ao deletar exame:', error.response?.data || error.message);
                alert('Erro ao deletar exame');
            }
        } else {
            alert('Você não está autenticado!');
        }
    };

    const deleteSerie = async (serieID) => {
        if (authData?.access) {
            const deleteUrl = `/api/v1/series/delete/${serieID}/`;  // A URL não precisa mais do patientID como parte da rota
            const config = {
                headers: {
                    'Authorization': `Bearer ${authData.access}`,
                    'Content-Type': 'application/json',
                },
            };

            try {
                await axios.delete(deleteUrl, config);
                alert('Exame deletado com sucesso');

                // Atualizar lista de exames após deletar
                ReceberExames();
            } catch (error) {
                console.error('Erro ao deletar exame:', error.response?.data || error.message);
                alert('Erro ao deletar exame');
            }
        } else {
            alert('Você não está autenticado!');
        }
    };

    return (
        <nav className={`text-center`}>
            <div className={`${styles.sideBarlimite}`}>
                <h2 className='mt-2' id={styles.LisaTextCtm}>
                    SATCT <IoIosLogOut onClick={logout} className={styles.logoutButton} />
                </h2>
                <div className={`${styles.sendDicomsDiv}  mx-2`}>
                    <span className={`${styles.sendDicoms} `}>Enviar </span>
                    <label htmlFor='sendDicoms' className={`${styles.sendDicomsLabel} ms-2`}>Escolher Exame</label>
                    <input
                        ref={fileInputRef}
                        type='file'
                        name="sendDicoms"
                        id='sendDicoms'
                        multiple
                        className={styles.sendDicomsCtm}
                        onChange={handleArquivos}
                    />
                    </div>

                <div className={`${styles.examsArea} mx-2`}>
                    <h3>Exames</h3>
                    {loading && (
                        <div className={styles.spinnerSidebar}> {/* Centraliza o spinner */}
                            <TailSpin
                                height={80}
                                width={80}
                                color="#00BFFF"
                                ariaLabel="tail-spin-loading"
                                radius="1"
                                wrapperStyle={{}}
                                wrapperClass=""
                                visible={true}
                            />
                        </div>
                    )}
                    {imageLinks.length === 0 ?
                        <div>nenhum exame encontrado</div>
                        :
                        imageLinks.map((elem, key) =>
                            <div key={key}>
                                {/* <div className={styles.serieName}>{elem.name} <button onClick={() => deleteExam(elem.series[0]?.patientId)}><FaTrash /> </button></div> */}
                                <div className={styles.serieDiv}>
                                    <span className={styles.serieName}>
                                        {elem.name}
                                    </span>
                                    <FaTrash className={styles.serieDeleteButton} onClick={() => deleteExam(elem.series[0]?.patientId)} />
                                </div>


                                {elem.series.map((serie, index) =>
                                    <div key={index}>
                                        <div className={styles.examLocal}>
                                            <p>{serie.serieDescription}</p>
                                            <div>
                                                <span onClick={() => deleteSerie(serie.seriesID)}><FaTrash /></span>
                                                <span draggable 
                                                    onDragStart={(e) => e.dataTransfer.setData('text/plain', JSON.stringify(serie))}><FaHandPaper /></span>
                                            </div>
                                        </div>

                                    </div>

                                )}

                            </div>)
                    }
                </div>
                <div className={`${styles.algorithmArea} m-2 py-2`}>
                    <label>  Escolha o Algoritmo</label>
                    <div>
                        <select name="cars" id="cars" onChange={(e) => setAlgoritmo(e.target.value)}>
                            <option value={1}>Segmentar pulmão</option>
                            <option value={2}>Classificação</option>
                            <option value={3}>[opção futura]</option>
                            <option value={4}>[opção futura]</option>
                        </select>
                        <button onClick={() => setExecutarSegmentacao(true)}>Ok</button>
                    </div>
                </div>
                <div className={`${styles.windowingArea} m-2 py-2`}></div>
                <label>  Janelamento </label>
                <div>
                    {/* <button id="plm" onClick={() => setWindowing(1)}>Pulmão</button>
                            <button id="osso" onClick={() => setWindowing(2)}>Osso</button>
                            <button id="torax" onClick={() => setWindowing(3)}>Tórax</button> */}
                    <button id="plm">Pulmão</button>
                    <button id="osso">Osso</button>
                    <button id="torax">Tórax</button>
                </div>
                <div className={`${styles.aerationCard} m-2 py-2`}>
                    <h4>Níveis de Aeração</h4>
                    <div className={`${styles.aerationOptions}`}>
                        <div className={`${styles.aerationOption}`}>
                            <input type="checkbox" id="hiper-aerada" />
                            <label htmlFor="hiper-aerada">Hiper Aerada</label>
                            <div className={styles.colorBox} style={{ backgroundColor: 'green' }}></div>
                        </div>
                        <div className={`${styles.aerationOption}`}>
                            <input type="checkbox" id="normalmente-aerada" />
                            <label htmlFor="normalmente-aerada">Normalmente Aerada</label>
                            <div className={styles.colorBox} style={{ backgroundColor: 'blue' }}></div>
                        </div>
                        <div className={`${styles.aerationOption}`}>
                            <input type="checkbox" id="pouco-aerada" />
                            <label htmlFor="pouco-aerada">Pouco Aerada</label>
                            <div className={styles.colorBox} style={{ backgroundColor: 'yellow' }}></div>
                        </div>
                        <div className={`${styles.aerationOption}`}>
                            <input type="checkbox" id="nada-aerada" />
                            <label htmlFor="nada-aerada">Nada Aerada</label>
                            <div className={styles.colorBox} style={{ backgroundColor: 'pink' }}></div>
                        </div>
                    </div>
                </div>
                    <div>
                        {/* <button id="plm" onClick={() => setWindowing(1)}>Pulmão</button>
                                <button id="osso" onClick={() => setWindowing(2)}>Osso</button>
                                <button id="torax" onClick={() => setWindowing(3)}>Tórax</button> */}
                        <button id="EnvRel">Enviar relatório</button>
                    </div>


            </div>


        </nav>
    );
}

export default SideBar