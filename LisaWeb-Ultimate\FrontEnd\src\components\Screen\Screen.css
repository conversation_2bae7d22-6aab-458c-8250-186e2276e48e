.tela-width-ctm {
    min-width: 300px;
    height: 70vh;
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    overflow: hidden;
}

.tela-ctm {
    /* height: 430px; */
    min-height: 250px;
    height: 100%;
    width: 60%;
    background-color: hsl(0, 0%, 50%);
    border-radius: 0px;
    position: relative;
}

.tela-border-ctm {
    position: relative;
    border: 7px solid hsl(0, 0%, 40%);
    border-radius: 20px;
}

.sliderSlicesCtm {
    position: absolute;
    width: 100%;
    bottom: 0px;
    right: 0px;
}

.selected-ctm {
    position: relative;
    border: 7px solid hsl(129, 100%, 50%);
    border-radius: 20px;
}

.classification-results {
    display: flex;
    flex-direction: column;
    gap: 20px;
    padding: 20px; /* Espaço interno para separar dos limites do container */
}

.result-section {
    background-color: #ffffff;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #dee2e6;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.result-section:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.result-section h3 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #343a40;
    font-size: 1.4rem;
}

.result-section p {
    margin: 0;
    font-size: 1rem;
    color: #6c757d;
    line-height: 1.5;
}


.spinner-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    /* Fundo semi-transparente */
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    /* Acima dos outros elementos do Screen */
}


.spinner-overlay {
    pointer-events: none;
}

/* Media query para telas menores */
@media (max-width: 800px) {
    .tela-width-ctm {
        width: 100%;
        min-width: 200px;
        max-height: 50%;
        /* Remove a limitação de largura máxima */
    }
}

/* Animação de ok ao corrigir */
.sinal-ok {
    position: absolute;
    top: 20px;
    right: 20px;
    background-color: #4CAF50;
    color: white;
    padding: 10px 20px;
    border-radius: 12px;
    font-size: 1.2rem;
    font-weight: bold;
    opacity: 0;
    animation: fadeInOut 2s ease-in-out;
    z-index: 1000;
}

@keyframes fadeInOut {
    0% {
        opacity: 0;
        transform: scale(0.9);
    }

    10% {
        opacity: 1;
        transform: scale(1);
    }

    90% {
        opacity: 1;
        transform: scale(1);
    }

    100% {
        opacity: 0;
        transform: scale(0.9);
    }
}