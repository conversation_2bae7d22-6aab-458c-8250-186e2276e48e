// react
import { useEffect } from "react";
// Cornerstone
import cornerstone from 'cornerstone-core';

export const handleJanelamento = (selecionada, idTela) => {
    useEffect(() => {
        // Verifica se o elemento existe
        const plmElement = document.getElementById('plm');
        const ossoElement = document.getElementById('osso');
        const toraxElement = document.getElementById('torax');

        // Define os manipuladores de eventos
        const plmHandler = () => {
            const element = document.getElementById(`dicomTela${idTela}`);
            if (element && selecionada) {
                cornerstone.enable(element);
                let viewport = cornerstone.getViewport(element);

                viewport.voi.windowWidth = 1500;
                viewport.voi.windowCenter = -600;
                cornerstone.setViewport(element, viewport);
                cornerstone.updateImage(element);
            }
        };

        const ossoHandler = () => {
            const element = document.getElementById(`dicomTela${idTela}`);
            if (element && selecionada) {
                cornerstone.enable(element);
                let viewport = cornerstone.getViewport(element);
                viewport.voi.windowWidth = 2500;
                viewport.voi.windowCenter = 480;
                cornerstone.setViewport(element, viewport);
                cornerstone.updateImage(element);
            }
        };

        const toraxHandler = () => {
            const element = document.getElementById(`dicomTela${idTela}`);
            if (element && selecionada) {
                cornerstone.enable(element);
                let viewport = cornerstone.getViewport(element);
                viewport.voi.windowWidth = 350;
                viewport.voi.windowCenter = 40;
                cornerstone.setViewport(element, viewport);
                cornerstone.updateImage(element);
            }
        };

        // Adiciona os event listeners somente se os elementos existirem
        if (plmElement) plmElement.addEventListener('click', plmHandler);
        if (ossoElement) ossoElement.addEventListener('click', ossoHandler);
        if (toraxElement) toraxElement.addEventListener('click', toraxHandler);

        // Função de limpeza para remover os listeners
        return () => {
            if (plmElement) plmElement.removeEventListener('click', plmHandler);
            if (ossoElement) ossoElement.removeEventListener('click', ossoHandler);
            if (toraxElement) toraxElement.removeEventListener('click', toraxHandler);
        };
    }, [selecionada]);
}
