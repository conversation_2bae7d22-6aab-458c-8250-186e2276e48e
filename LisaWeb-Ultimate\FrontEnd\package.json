{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@cornerstonejs/core": "0.38.3", "@cornerstonejs/dicom-image-loader": "^3.12.2", "@cornerstonejs/tools": "^0.59.0", "axios": "^1.7.2", "bootstrap": "^5.3.3", "cornerstone-core": "^2.6.1", "cornerstone-math": "^0.1.10", "cornerstone-tools": "^6.0.10", "cornerstone-wado-image-loader": "^4.13.2", "dicom-parser": "^1.8.21", "hammerjs": "^2.0.8", "react": "^18.3.1", "react-bootstrap": "^2.10.3", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.3.1", "react-draggable": "^4.4.6", "react-icons": "^5.2.1", "react-loader-spinner": "^6.1.6", "react-modal": "^3.16.1", "react-router-dom": "^6.24.1", "sweetalert2": "^11.17.2"}, "devDependencies": {"@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "eslint": "^8.57.0", "eslint-plugin-react": "^7.34.2", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.7", "vite": "^5.3.1"}}