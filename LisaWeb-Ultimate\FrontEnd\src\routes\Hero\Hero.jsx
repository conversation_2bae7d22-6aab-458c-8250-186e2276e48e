import './Hero.css';
import graficos from '/trueone.png';
import lisaTela from '/lisaTle.png';

function Hero() {


    return (
        <main className='Hero-main'>
            <h2>Automatize o Processo!</h2>
            <h1>Máxima eficiência e agilidade: impulsione sua <span>produtividade</span> com nossa <span>IA</span></h1>

            <div className='Hero-buttons'>
                <button><PERSON>e Grátis!</button>
                <button>Fale Conosco!</button>
            </div>
            <img src={graficos} alt="graficos" className="Hero-graficos" />


            <h1 className='vendas-title'>o que podemos oferecer</h1>
            <div className="frame-vendas">
                <div className="imagem-vendas">
                    <img src={lisaTela} alt="Imagem ilustrativa" />
                </div>
                <div className="beneficios-vendas">
                    <h2>Por que escolher nossa solução?</h2>
                    <ul>
                        {[
                            "Processamento Rápido e Seguro de imagens.",
                            "Compatível com Formatos DICOM.",
                            "Integração com Sistemas Hospitalares.",
                            "A melhor IA do mercado",
                            "Transforme seus laudos com IA",
                            "Diagnósticos mais rápidos e confiáveis",
                            "Segurança total para suas imagens médicas",
                            "Inovação contínua para melhores resultados.",
                            "A solução completa para análise de imagens médicas",
                            "Eleve o padrão dos seus laudos."
                        ].map((frase, index) => (
                            <li key={index}>
                                <span className="icone">✔</span>
                                {frase}
                            </li>
                        ))}
                    </ul>
                </div>
            </div>

        </main>
    );
}

export default Hero;