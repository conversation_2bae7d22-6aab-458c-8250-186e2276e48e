//css
import '../style.css';
//react and react router
import { useState, useContext, useEffect } from 'react';
import Screen from '../../components/Screen/Screen.jsx';
import { useNavigate } from 'react-router-dom';
//components
import PersonalScreen from '../../components/PersonalScreen.jsx'
import cornerstone from 'cornerstone-core';
import cornerstoneTools from 'cornerstone-tools';
import SideBar from '../../components/SideBar.jsx';
//context
import { AuthContext } from '../../context/AuthProvider.jsx';
import { FaExpandAlt } from "react-icons/fa";
import { RiMenu2Line } from "react-icons/ri";


function Home() {
    const [tela, setTela] = useState(1);
    const { authData } = useContext(AuthContext);
    const [teleBloqueada, setTelaBloqueada] = useState(true);
    const [executarSegmentacao, setExecutarSegmentacao] = useState(false);
    const navigate = useNavigate();

    const [algoritmo, setAlgoritmo] = useState(1);
    const [sidebarVisivel, setSidebarVisivel] = useState(true);
    const [aerationCustom, setAerationCustom] = useState({
        min: 0,
        max: 0,
        color: "#000000",
        enabled: false, // Novo estado
    });

    const [reportNotes, setReportNotes] = useState("");
    const [doctorCRM, setDoctorCRM] = useState("");


    const redirecionarSeNaoLogado = () => {
        const storedAuthData = JSON.parse(localStorage.getItem('authData'));

        if ((authData !== null && authData !== undefined) || (storedAuthData !== undefined && storedAuthData !== null)) {
            setTelaBloqueada(false)
        } else {
            navigate("/");
        }
    }

    useEffect(() => {
        redirecionarSeNaoLogado();
    }, [authData]);

    const selecionarTela = (telaId) => {
        setTela(telaId);
    }

    const [isFreeToolActive, setIsFreeToolActive] = useState(false);
    const [isOpen, setIsOpen] = useState(false);
    const activateFreeTool = () => {
        const element = document.getElementById(`dicomTela${tela}`);
        cornerstone.enable(element);
        cornerstoneTools.setToolActiveForElement(element, 'FreehandRoi', { mouseButtonMask: 1 });
        setIsFreeToolActive(true);
    };

    const deactivateFreeTool = () => {
        const element = document.getElementById(`dicomTela${tela}`);
        cornerstone.enable(element);
        cornerstoneTools.setToolDisabledForElement(element, 'FreehandRoi');
        setIsFreeToolActive(false);
    };

    const lidarFree = () => {
        if (isFreeToolActive) {
            deactivateFreeTool();
        } else {
            activateFreeTool();
        }
    };

    const openPanel = () => {
        setIsOpen(true);
    };

    const closePanel = () => {
        setIsOpen(false);
    };

    // useEffect(() => {
    //     console.log(tela);
    // }, [tela]);
    return (
        <div>
            {teleBloqueada ?
                <div>
                    <p>É necessário autenticação</p>
                </div>
                :
                <div className='home-ctm'>
                    <RiMenu2Line onClick={() => setSidebarVisivel(!sidebarVisivel)} >
                        <FaExpandAlt size={30} />
                        {sidebarVisivel ? '' : ''}
                    </RiMenu2Line >

                    <section className={`${sidebarVisivel ? 'sidebarExpandido' : 'sidebarComprimido'}`}>
                        <SideBar setAlgoritmo={setAlgoritmo}
                            setExecutarSegmentacao={setExecutarSegmentacao}
                            executarSegmentacao={executarSegmentacao}
                            aerationCustom={aerationCustom}
                            setAerationCustom={setAerationCustom}
                            reportNotes={reportNotes}
                            doctorCRM={doctorCRM} />
                    </section>


                    <section className={`section-ctm ${sidebarVisivel ? '' : 'expandido'}`}>
                        {[1, 2].map(id => (
                            <Screen
                                sidebarVisivel={sidebarVisivel}
                                algoritmo={algoritmo}
                                executarSegmentacao={executarSegmentacao}
                                setExecutarSegmentacao={setExecutarSegmentacao}
                                key={id}
                                idTela={id}
                                selecionada={tela === id}
                                tela={tela}
                                onClick={() => selecionarTela(id)}
                                lidarFree={lidarFree}
                                aerationCustom={aerationCustom}
                                setAerationCustom={setAerationCustom}
                                setReportNotes={setReportNotes}
                                setDoctorCRM={setDoctorCRM}
                            />
                        ))}
                    </section>

                </div>
            }

        </div>
    );
}

export default Home