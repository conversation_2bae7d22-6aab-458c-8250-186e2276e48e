// src/context/AerationContext.jsx
import React, { createContext, useState } from 'react';

export const AerationContext = createContext();

export const AerationProvider = ({ children }) => {
    const [aerationCustom, setAerationCustom] = useState({
        min: 0,
        max: 0,
        color: "#000000",
        enabled: false,
    });

    const updateAerationCustom = (field, value) => {
        setAerationCustom(prev => ({
            ...prev,
            [field]: value,
        }));
    };

    const toggleAerationCustom = () => {
        setAerationCustom(prev => ({
            ...prev,
            enabled: !prev.enabled,
        }));
    };

    return (
        <AerationContext.Provider value={{
            aerationCustom,
            updateAerationCustom,
            toggleAerationCustom
        }}>
            {children}
        </AerationContext.Provider>
    );
};
