.resetPassword {
    height: 100vh;
    width: 100vw;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #EBE5E0;
}

.resetPasswordForm {
    background-color: white;
    width: 450px;
    min-width: 400px;
    border: 2px solid #13D8A3;
    border-radius: 20px;
}

.resetPasswordForm h2 {
    text-align: center;
    font-weight: 600;
    font-size: 2rem;
    margin-top: -10px;
    color: #333;
}

.resetPasswordForm p {
    text-align: center;
    margin-bottom: 20px;
}

#buttonCtm {
    background-color: #13D8A3;
    color: white;
    width: 100%;
    padding: 10px;
    font-weight: 500;
    transition: background-color 0.3s;
}

#buttonCtm:hover:not(:disabled) {
    background-color: #10c090;
}

#buttonCtm:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
}

.passwordRequirements {
    margin-top: 5px;
    padding: 10px;
    border-radius: 5px;
    background-color: #f8f9fa;
    font-size: 0.9rem;
}

.passwordRequirements.show {
    display: block;
}

.passwordRequirements.hide {
    display: none;
}

.passwordRequirements ul {
    margin-top: 5px;
    padding-left: 25px;
}

.passwordRequirements li {
    margin-bottom: 3px;
}

/* Estilização dos inputs */
.resetPasswordForm input:focus {
    outline: none;
    border: 0px;
    box-shadow: 0 0 0 0.2rem #13D8A3;
}

/* Link para voltar ao login */
.resetPasswordForm a {
    color: #13D8A3;
    text-decoration: underline !important;
    text-underline-offset: 3px;
}

/* Ícones de validação */
.text-success {
    color: #28a745 !important;
}

.text-danger {
    color: #dc3545 !important;
}

.ms-2 {
    margin-left: 0.5rem !important;
}

@media (max-width:600px) {
    .resetPasswordForm {
        min-width: 300px;
    }
}
