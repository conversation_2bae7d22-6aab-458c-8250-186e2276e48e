// react
import { useEffect } from "react";
// Cornerstone
import cornerstone from 'cornerstone-core';

export const handleAeracao = (contornosFiltrados, selecionada, arquivos, idTela, aerationCustom) => {
    const maskStates = {
        'hiper-aerada': false,
        'normalmente-aerada': false,
        'pouco-aerada': false,
        'nada-aerada': false,
        'custom-aerada': false,
    };
    useEffect(() => {
        const hiperElement = document.getElementById('hiper-aerada');
        const normalElement = document.getElementById('normalmente-aerada');
        const poucoElement = document.getElementById('pouco-aerada');
        const nadaElement = document.getElementById('nada-aerada');
        const customElement = document.getElementById('custom-aeration');
        const hiperHandler = (event) => {
            const element = document.getElementById(`dicomTela${idTela}`);
            if (event.target.checked) {
                maskStates['hiper-aerada'] = true;
                console.log('contornosFiltrados', contornosFiltrados);
                const limites = contornosFiltrados.contornos;
                console.log('Limites:', limites);

                // Função para verificar se um ponto está dentro de um polígono
                function isPointInPolygon(x, y, polygon) {
                    let inside = false;
                    const numPoints = polygon.length;
                    let j = numPoints - 1;

                    for (let i = 0; i < numPoints; i++) {
                        const xi = polygon[i][0], yi = polygon[i][1];
                        const xj = polygon[j][0], yj = polygon[j][1];

                        const intersect = ((yi > y) !== (yj > y)) &&
                            (x < ((xj - xi) * (y - yi)) / (yj - yi + 0.0000001) + xi);

                        if (intersect) inside = !inside;
                        j = i;
                    }

                    return inside;
                }

                // Função para obter o índice da imagem atual
                function getCurrentImageIndex() {
                    const image = cornerstone.getImage(element);
                    const imageId = image.imageId;
                    const imageIdIndex = arquivos.indexOf(imageId.substring(8)); // Ajuste conforme necessário
                    const atual = imageIdIndex;
                    return atual;
                }

                // Função para computar a máscara utilizando apenas os polígonos da imagem atual
                function computeMaskData() {
                    const image = cornerstone.getImage(element);
                    const pixelData = image.getPixelData();
                    const rescaleSlope = image.slope;
                    const rescaleIntercept = image.intercept;

                    function toHU(pixelValue) {
                        return pixelValue * rescaleSlope + rescaleIntercept;
                    }

                    const airThresholdMin = -1000;
                    const airThresholdMax = -901;
                    const width = image.width;
                    const height = image.height;
                    const numPixels = pixelData.length;

                    const maskData = new Uint8ClampedArray(numPixels * 4);

                    // Obter o índice da imagem atual
                    const atual = getCurrentImageIndex();

                    // Obter os polígonos para a imagem atual
                    const polygons = limites[atual];

                    if (!Array.isArray(polygons) || polygons.length === 0) {
                        console.log('Nenhum polígono encontrado para a imagem atual.');
                        element._offscreenCanvasHiperAerada = null;
                        return;
                    }

                    // Calcular o retângulo delimitador para otimizar a iteração
                    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
                    polygons.forEach((polygon) => {
                        polygon.forEach((point) => {
                            const x = point[0];
                            const y = point[1];
                            if (x < minX) minX = x;
                            if (y < minY) minY = y;
                            if (x > maxX) maxX = x;
                            if (y > maxY) maxY = y;
                        });
                    });

                    // Garantir que os limites estão dentro das dimensões da imagem
                    minX = Math.max(0, Math.floor(minX));
                    minY = Math.max(0, Math.floor(minY));
                    maxX = Math.min(width - 1, Math.ceil(maxX));
                    maxY = Math.min(height - 1, Math.ceil(maxY));

                    // Iterar sobre os pixels dentro dos limites
                    for (let y = minY; y <= maxY; y++) {
                        for (let x = minX; x <= maxX; x++) {
                            const i = y * width + x;

                            let isInsideAnyPolygon = false;

                            // Verificar se o pixel está dentro de algum polígono
                            for (let polygon of polygons) {
                                if (isPointInPolygon(x, y, polygon)) {
                                    isInsideAnyPolygon = true;
                                    break;
                                }
                            }

                            if (isInsideAnyPolygon) {
                                const huValue = toHU(pixelData[i]);

                                if (huValue >= airThresholdMin && huValue <= airThresholdMax) {
                                    maskData[i * 4] = 0;       // R
                                    maskData[i * 4 + 1] = 255; // G
                                    maskData[i * 4 + 2] = 0;   // B
                                    maskData[i * 4 + 3] = 255; // A
                                } else {
                                    maskData[i * 4 + 3] = 0;   // A
                                }
                            } else {
                                maskData[i * 4 + 3] = 0;   // A
                            }
                        }
                    }

                    const imageData = new ImageData(maskData, width, height);

                    const offscreenCanvas = document.createElement('canvas');
                    offscreenCanvas.width = width;
                    offscreenCanvas.height = height;
                    const offscreenCtx = offscreenCanvas.getContext('2d');
                    offscreenCtx.putImageData(imageData, 0, 0);

                    element._offscreenCanvasHiperAerada = offscreenCanvas;
                }

                // Função chamada quando a imagem é renderizada
                const onImageRendered = function (event) {
                    if (!maskStates['hiper-aerada']) return;

                    const element = event.target;
                    const context = event.detail.canvasContext;
                    const viewport = cornerstone.getViewport(element);
                    const image = cornerstone.getImage(element);

                    // Verificar se o offscreenCanvas existe
                    if (!element._offscreenCanvasHiperAerada) {
                        return;
                    }

                    context.save();

                    context.setTransform(1, 0, 0, 1, 0, 0);

                    const canvas = context.canvas;
                    const canvasWidth = canvas.width;
                    const canvasHeight = canvas.height;

                    const imageWidth = image.width;
                    const imageHeight = image.height;

                    const scale = viewport.scale;
                    const rotation = (viewport.rotation || 0) * Math.PI / 180;
                    const hflip = viewport.hflip ? -1 : 1;
                    const vflip = viewport.vflip ? -1 : 1;
                    const translateX = viewport.translation.x;
                    const translateY = viewport.translation.y;

                    context.translate(canvasWidth / 2, canvasHeight / 2);

                    context.rotate(rotation);

                    context.scale(hflip, vflip);

                    context.scale(scale, scale);

                    context.translate(translateX, translateY);

                    context.translate(-imageWidth / 2, -imageHeight / 2);

                    context.drawImage(
                        element._offscreenCanvasHiperAerada,
                        0, 0, imageWidth, imageHeight, // Source
                        0, 0, imageWidth, imageHeight  // Destination
                    );

                    context.restore();
                };

                // Função chamada quando uma nova imagem é carregada
                const onNewImage = function (event) {
                    computeMaskData();
                };

                if (element) {
                    element.addEventListener('cornerstoneimagerendered', onImageRendered);
                    element.addEventListener('cornerstonenewimage', onNewImage);
                  
                    element._onImageRenderedHandlerHiperAerada = onImageRendered;
                    element._onNewImageHandlerHiperAerada = onNewImage;
                  
                    computeMaskData();
                  } else {
                    console.warn('Elemento não encontrado para adicionar event listeners.');
                  }

                cornerstone.updateImage(element);

                console.log('Máscara aplicada e event listeners adicionados.');
            } else {
                maskStates['hiper-aerada'] = false;

                if (element._onImageRenderedHandlerHiperAerada) {
                    element.removeEventListener('cornerstoneimagerendered', element._onImageRenderedHandlerHiperAerada);
                    delete element._onImageRenderedHandlerHiperAerada;
                }

                if (element._onNewImageHandlerHiperAerada) {
                    element.removeEventListener('cornerstonenewimage', element._onNewImageHandlerHiperAerada);
                    delete element._onNewImageHandlerHiperAerada;
                }

                delete element._offscreenCanvasHiperAerada;

                cornerstone.updateImage(element);
            }
        }
        const normalHandler = (event) => {
            const element = document.getElementById(`dicomTela${idTela}`);

            if (event.target.checked) {
                maskStates['normalmente-aerada'] = true;

                const limitesGerais = contornosFiltrados.contornos; // Ajuste conforme a estrutura dos seus dados

                // Função para verificar se um ponto está dentro de um polígono
                function isPointInPolygon(x, y, polygon) {
                    let inside = false;
                    const numPoints = polygon.length;
                    let j = numPoints - 1;

                    for (let i = 0; i < numPoints; i++) {
                        const xi = polygon[i][0], yi = polygon[i][1];
                        const xj = polygon[j][0], yj = polygon[j][1];

                        const intersect = ((yi > y) !== (yj > y)) &&
                            (x < ((xj - xi) * (y - yi)) / (yj - yi + 0.0000001) + xi);

                        if (intersect) inside = !inside;
                        j = i;
                    }

                    return inside;
                }

                // Função para obter o índice da imagem atual
                function getCurrentImageIndex() {
                    const image = cornerstone.getImage(element);
                    const imageId = image.imageId;
                    const imageIdIndex = arquivos.indexOf(imageId.substring(8)); // Ajuste conforme necessário
                    const atual = imageIdIndex;
                    return atual;
                }

                function computeMaskData() {
                    const image = cornerstone.getImage(element);
                    const pixelData = image.getPixelData();
                    const rescaleSlope = image.slope;
                    const rescaleIntercept = image.intercept;

                    function toHU(pixelValue) {
                        return pixelValue * rescaleSlope + rescaleIntercept;
                    }

                    const airThresholdMin = -900;
                    const airThresholdMax = -501;
                    const width = image.width;
                    const height = image.height;
                    const numPixels = pixelData.length;

                    const maskData = new Uint8ClampedArray(numPixels * 4);

                    // Obter o índice da imagem atual
                    const atual = getCurrentImageIndex();

                    // Obter os polígonos para a imagem atual
                    const polygons = limitesGerais[atual];

                    if (!Array.isArray(polygons) || polygons.length === 0) {
                        console.log('Nenhum polígono encontrado para a imagem atual.');
                        element._offscreenCanvasNormal = null;
                        return;
                    }

                    // Calcular o retângulo delimitador para otimizar a iteração
                    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
                    polygons.forEach((polygon) => {
                        polygon.forEach((point) => {
                            const x = point[0];
                            const y = point[1];
                            if (x < minX) minX = x;
                            if (y < minY) minY = y;
                            if (x > maxX) maxX = x;
                            if (y > maxY) maxY = y;
                        });
                    });

                    // Garantir que os limites estão dentro das dimensões da imagem
                    minX = Math.max(0, Math.floor(minX));
                    minY = Math.max(0, Math.floor(minY));
                    maxX = Math.min(width - 1, Math.ceil(maxX));
                    maxY = Math.min(height - 1, Math.ceil(maxY));

                    // Iterar sobre os pixels dentro dos limites
                    for (let y = minY; y <= maxY; y++) {
                        for (let x = minX; x <= maxX; x++) {
                            const i = y * width + x;

                            let isInsideAnyPolygon = false;

                            // Verificar se o pixel está dentro de algum polígono
                            for (let polygon of polygons) {
                                if (isPointInPolygon(x, y, polygon)) {
                                    isInsideAnyPolygon = true;
                                    break;
                                }
                            }

                            if (isInsideAnyPolygon) {
                                const huValue = toHU(pixelData[i]);

                                if (huValue >= airThresholdMin && huValue <= airThresholdMax) {
                                    maskData[i * 4] = 0;       // R
                                    maskData[i * 4 + 1] = 0;   // G
                                    maskData[i * 4 + 2] = 250; // B
                                    maskData[i * 4 + 3] = 255; // A
                                } else {
                                    maskData[i * 4 + 3] = 0;   // A
                                }
                            } else {
                                maskData[i * 4 + 3] = 0;   // A
                            }
                        }
                    }

                    const imageData = new ImageData(maskData, width, height);

                    const offscreenCanvas = document.createElement('canvas');
                    offscreenCanvas.width = width;
                    offscreenCanvas.height = height;
                    const offscreenCtx = offscreenCanvas.getContext('2d');
                    offscreenCtx.putImageData(imageData, 0, 0);

                    element._offscreenCanvasNormal = offscreenCanvas;
                }

                computeMaskData();

                const onImageRendered = function (event) {
                    if (!maskStates['normalmente-aerada']) return;

                    const element = event.target;
                    const context = event.detail.canvasContext;
                    const viewport = cornerstone.getViewport(element);
                    const image = cornerstone.getImage(element);

                    // Verificar se o offscreenCanvas existe
                    if (!element._offscreenCanvasNormal) {
                        return;
                    }

                    context.save();

                    context.setTransform(1, 0, 0, 1, 0, 0);

                    const canvas = context.canvas;
                    const canvasWidth = canvas.width;
                    const canvasHeight = canvas.height;

                    const imageWidth = image.width;
                    const imageHeight = image.height;

                    const scale = viewport.scale;
                    const rotation = (viewport.rotation || 0) * Math.PI / 180;
                    const hflip = viewport.hflip ? -1 : 1;
                    const vflip = viewport.vflip ? -1 : 1;
                    const translateX = viewport.translation.x;
                    const translateY = viewport.translation.y;

                    context.translate(canvasWidth / 2, canvasHeight / 2);

                    context.rotate(rotation);

                    context.scale(hflip, vflip);

                    context.scale(scale, scale);

                    context.translate(translateX, translateY);

                    context.translate(-imageWidth / 2, -imageHeight / 2);

                    context.drawImage(
                        element._offscreenCanvasNormal,
                        0, 0, imageWidth, imageHeight, // Source
                        0, 0, imageWidth, imageHeight  // Destination
                    );

                    context.restore();
                };

                const onNewImage = function (event) {
                    computeMaskData();
                };

                element.addEventListener('cornerstoneimagerendered', onImageRendered);
                element.addEventListener('cornerstonenewimage', onNewImage);

                element._onImageRenderedHandlerNormal = onImageRendered;
                element._onNewImageHandlerNormal = onNewImage;

                cornerstone.updateImage(element);

                console.log('Máscara aplicada e event listeners adicionados.');
            } else {
                maskStates['normalmente-aerada'] = false;

                if (element._onImageRenderedHandlerNormal) {
                    element.removeEventListener('cornerstoneimagerendered', element._onImageRenderedHandlerNormal);
                    delete element._onImageRenderedHandlerNormal;
                }

                if (element._onNewImageHandlerNormal) {
                    element.removeEventListener('cornerstonenewimage', element._onNewImageHandlerNormal);
                    delete element._onNewImageHandlerNormal;
                }

                delete element._offscreenCanvasNormal;

                cornerstone.updateImage(element);
            }
        }

        function hexToRGBA() {
            // Remove o '#' se estiver presente
            if (aerationCustom.color.charAt(0) === "#") {
                aerationCustom.color = aerationCustom.color.slice(1);
            }

            // Se for notação curta (#000), expande para notação completa (#000000)
            if (aerationCustom.color.length === 3) {
                aerationCustom.color = aerationCustom.color.split('').map(char => char + char).join('');
            }

            // Pega os valores de R, G e B
            const r = parseInt(aerationCustom.color.slice(0, 2), 16);
            const g = parseInt(aerationCustom.color.slice(2, 4), 16);
            const b = parseInt(aerationCustom.color.slice(4, 6), 16);
            const a = 255;

            return [r, g, b, a];
        }

        const poucoHandler = (event) => {
            const element = document.getElementById(`dicomTela${idTela}`);

            if (event.target.checked) {
                maskStates['pouco-aerada'] = true;

                const limitesGerais = contornosFiltrados.contornos; // Ajuste conforme a estrutura dos seus dados

                // Função para verificar se um ponto está dentro de um polígono
                function isPointInPolygon(x, y, polygon) {
                    let inside = false;
                    const numPoints = polygon.length;
                    let j = numPoints - 1;

                    for (let i = 0; i < numPoints; i++) {
                        const xi = polygon[i][0], yi = polygon[i][1];
                        const xj = polygon[j][0], yj = polygon[j][1];

                        const intersect = ((yi > y) !== (yj > y)) &&
                            (x < ((xj - xi) * (y - yi)) / (yj - yi + 0.0000001) + xi);

                        if (intersect) inside = !inside;
                        j = i;
                    }

                    return inside;
                }

                // Função para obter o índice da imagem atual
                function getCurrentImageIndex() {
                    const image = cornerstone.getImage(element);
                    const imageId = image.imageId;
                    const imageIdIndex = arquivos.indexOf(imageId.substring(8)); // Ajuste conforme necessário
                    const atual = imageIdIndex;
                    return atual;
                }

                function computeMaskData() {
                    const image = cornerstone.getImage(element);
                    const pixelData = image.getPixelData();
                    const rescaleSlope = image.slope;
                    const rescaleIntercept = image.intercept;

                    function toHU(pixelValue) {
                        return pixelValue * rescaleSlope + rescaleIntercept;
                    }

                    const airThresholdMin = -500;
                    const airThresholdMax = 99;
                    const width = image.width;
                    const height = image.height;
                    const numPixels = pixelData.length;

                    const maskData = new Uint8ClampedArray(numPixels * 4);

                    // Obter o índice da imagem atual
                    const atual = getCurrentImageIndex();

                    // Obter os polígonos para a imagem atual
                    const polygons = limitesGerais[atual];

                    if (!Array.isArray(polygons) || polygons.length === 0) {
                        console.log('Nenhum polígono encontrado para a imagem atual.');
                        element._offscreenCanvasPoucoAerada = null;
                        return;
                    }

                    // Calcular o retângulo delimitador para otimizar a iteração
                    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
                    polygons.forEach((polygon) => {
                        polygon.forEach((point) => {
                            const x = point[0];
                            const y = point[1];
                            if (x < minX) minX = x;
                            if (y < minY) minY = y;
                            if (x > maxX) maxX = x;
                            if (y > maxY) maxY = y;
                        });
                    });

                    // Garantir que os limites estão dentro das dimensões da imagem
                    minX = Math.max(0, Math.floor(minX));
                    minY = Math.max(0, Math.floor(minY));
                    maxX = Math.min(width - 1, Math.ceil(maxX));
                    maxY = Math.min(height - 1, Math.ceil(maxY));

                    // Iterar sobre os pixels dentro dos limites
                    for (let y = minY; y <= maxY; y++) {
                        for (let x = minX; x <= maxX; x++) {
                            const i = y * width + x;

                            let isInsideAnyPolygon = false;

                            // Verificar se o pixel está dentro de algum polígono
                            for (let polygon of polygons) {
                                if (isPointInPolygon(x, y, polygon)) {
                                    isInsideAnyPolygon = true;
                                    break;
                                }
                            }

                            if (isInsideAnyPolygon) {
                                const huValue = toHU(pixelData[i]);

                                if (huValue >= airThresholdMin && huValue <= airThresholdMax) {
                                    maskData[i * 4] = 255;     // R
                                    maskData[i * 4 + 1] = 255; // G
                                    maskData[i * 4 + 2] = 0;   // B
                                    maskData[i * 4 + 3] = 250; // A
                                } else {
                                    maskData[i * 4 + 3] = 0;   // A
                                }
                            } else {
                                maskData[i * 4 + 3] = 0;   // A
                            }
                        }
                    }

                    const imageData = new ImageData(maskData, width, height);

                    const offscreenCanvas = document.createElement('canvas');
                    offscreenCanvas.width = width;
                    offscreenCanvas.height = height;
                    const offscreenCtx = offscreenCanvas.getContext('2d');
                    offscreenCtx.putImageData(imageData, 0, 0);

                    element._offscreenCanvasPoucoAerada = offscreenCanvas;
                }

                computeMaskData();

                const onImageRendered = function (event) {
                    if (!maskStates['pouco-aerada']) return;

                    const element = event.target;
                    const context = event.detail.canvasContext;
                    const viewport = cornerstone.getViewport(element);
                    const image = cornerstone.getImage(element);

                    // Verificar se o offscreenCanvas existe
                    if (!element._offscreenCanvasPoucoAerada) {
                        return;
                    }

                    context.save();

                    context.setTransform(1, 0, 0, 1, 0, 0);

                    const canvas = context.canvas;
                    const canvasWidth = canvas.width;
                    const canvasHeight = canvas.height;

                    const imageWidth = image.width;
                    const imageHeight = image.height;

                    const scale = viewport.scale;
                    const rotation = (viewport.rotation || 0) * Math.PI / 180;
                    const hflip = viewport.hflip ? -1 : 1;
                    const vflip = viewport.vflip ? -1 : 1;
                    const translateX = viewport.translation.x;
                    const translateY = viewport.translation.y;

                    context.translate(canvasWidth / 2, canvasHeight / 2);

                    context.rotate(rotation);

                    context.scale(hflip, vflip);

                    context.scale(scale, scale);

                    context.translate(translateX, translateY);

                    context.translate(-imageWidth / 2, -imageHeight / 2);

                    context.drawImage(
                        element._offscreenCanvasPoucoAerada,
                        0, 0, imageWidth, imageHeight, // Source
                        0, 0, imageWidth, imageHeight  // Destination
                    );

                    context.restore();
                };

                const onNewImage = function (event) {
                    computeMaskData();
                };

                element.addEventListener('cornerstoneimagerendered', onImageRendered);
                element.addEventListener('cornerstonenewimage', onNewImage);

                element._onImageRenderedHandlerPoucoAerada = onImageRendered;
                element._onNewImageHandlerPoucoAerada = onNewImage;

                cornerstone.updateImage(element);

                console.log('Máscara aplicada e event listeners adicionados.');
            } else {
                maskStates['pouco-aerada'] = false;

                if (element._onImageRenderedHandlerPoucoAerada) {
                    element.removeEventListener('cornerstoneimagerendered', element._onImageRenderedHandlerPoucoAerada);
                    delete element._onImageRenderedHandlerPoucoAerada;
                }

                if (element._onNewImageHandlerPoucoAerada) {
                    element.removeEventListener('cornerstonenewimage', element._onNewImageHandlerPoucoAerada);
                    delete element._onNewImageHandlerPoucoAerada;
                }

                delete element._offscreenCanvasPoucoAerada;

                cornerstone.updateImage(element);
            }
        }

        const nadaHandler = (event) => {
            const element = document.getElementById(`dicomTela${idTela}`);

            if (event.target.checked) {
                maskStates['nada-aerada'] = true;

                const limitesGerais = contornosFiltrados.contornos; // Ajuste conforme a estrutura dos seus dados

                // Função para verificar se um ponto está dentro de um polígono
                function isPointInPolygon(x, y, polygon) {
                    let inside = false;
                    const numPoints = polygon.length;
                    let j = numPoints - 1;

                    for (let i = 0; i < numPoints; i++) {
                        const xi = polygon[i][0], yi = polygon[i][1];
                        const xj = polygon[j][0], yj = polygon[j][1];

                        const intersect = ((yi > y) !== (yj > y)) &&
                            (x < ((xj - xi) * (y - yi)) / (yj - yi + 0.0000001) + xi);

                        if (intersect) inside = !inside;
                        j = i;
                    }

                    return inside;
                }

                // Função para obter o índice da imagem atual
                function getCurrentImageIndex() {
                    const image = cornerstone.getImage(element);
                    const imageId = image.imageId;
                    const imageIdIndex = arquivos.indexOf(imageId.substring(8)); // Ajuste conforme necessário
                    const atual = imageIdIndex;
                    return atual;
                }

                function computeMaskData() {
                    const image = cornerstone.getImage(element);
                    const pixelData = image.getPixelData();
                    const rescaleSlope = image.slope;
                    const rescaleIntercept = image.intercept;

                    function toHU(pixelValue) {
                        return pixelValue * rescaleSlope + rescaleIntercept;
                    }

                    const airThresholdMin = 100;
                    const airThresholdMax = 2000;
                    const width = image.width;
                    const height = image.height;
                    const numPixels = pixelData.length;

                    const maskData = new Uint8ClampedArray(numPixels * 4);

                    // Obter o índice da imagem atual
                    const atual = getCurrentImageIndex();

                    // Obter os polígonos para a imagem atual
                    const polygons = limitesGerais[atual];

                    if (!Array.isArray(polygons) || polygons.length === 0) {
                        console.log('Nenhum polígono encontrado para a imagem atual.');
                        element._offscreenCanvasNadaAerada = null;
                        return;
                    }

                    // Calcular o retângulo delimitador para otimizar a iteração
                    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
                    polygons.forEach((polygon) => {
                        polygon.forEach((point) => {
                            const x = point[0];
                            const y = point[1];
                            if (x < minX) minX = x;
                            if (y < minY) minY = y;
                            if (x > maxX) maxX = x;
                            if (y > maxY) maxY = y;
                        });
                    });

                    // Garantir que os limites estão dentro das dimensões da imagem
                    minX = Math.max(0, Math.floor(minX));
                    minY = Math.max(0, Math.floor(minY));
                    maxX = Math.min(width - 1, Math.ceil(maxX));
                    maxY = Math.min(height - 1, Math.ceil(maxY));

                    // Iterar sobre os pixels dentro dos limites
                    for (let y = minY; y <= maxY; y++) {
                        for (let x = minX; x <= maxX; x++) {
                            const i = y * width + x;

                            let isInsideAnyPolygon = false;

                            // Verificar se o pixel está dentro de algum polígono
                            for (let polygon of polygons) {
                                if (isPointInPolygon(x, y, polygon)) {
                                    isInsideAnyPolygon = true;
                                    break;
                                }
                            }

                            if (isInsideAnyPolygon) {
                                const huValue = toHU(pixelData[i]);

                                if (huValue >= airThresholdMin && huValue <= airThresholdMax) {
                                    maskData[i * 4] = 250;       // R
                                    maskData[i * 4 + 1] = 0;     // G
                                    maskData[i * 4 + 2] = 250;   // B
                                    maskData[i * 4 + 3] = 250;   // A
                                } else {
                                    maskData[i * 4 + 3] = 0;     // A
                                }
                            } else {
                                maskData[i * 4 + 3] = 0;     // A
                            }
                        }
                    }

                    const imageData = new ImageData(maskData, width, height);

                    const offscreenCanvas = document.createElement('canvas');
                    offscreenCanvas.width = width;
                    offscreenCanvas.height = height;
                    const offscreenCtx = offscreenCanvas.getContext('2d');
                    offscreenCtx.putImageData(imageData, 0, 0);

                    element._offscreenCanvasNadaAerada = offscreenCanvas;
                }

                computeMaskData();

                const onImageRendered = function (event) {
                    if (!maskStates['nada-aerada']) return;

                    const element = event.target;
                    const context = event.detail.canvasContext;
                    const viewport = cornerstone.getViewport(element);
                    const image = cornerstone.getImage(element);

                    // Verificar se o offscreenCanvas existe
                    if (!element._offscreenCanvasNadaAerada) {
                        return;
                    }

                    context.save();

                    context.setTransform(1, 0, 0, 1, 0, 0);

                    const canvas = context.canvas;
                    const canvasWidth = canvas.width;
                    const canvasHeight = canvas.height;

                    const imageWidth = image.width;
                    const imageHeight = image.height;

                    const scale = viewport.scale;
                    const rotation = (viewport.rotation || 0) * Math.PI / 180;
                    const hflip = viewport.hflip ? -1 : 1;
                    const vflip = viewport.vflip ? -1 : 1;
                    const translateX = viewport.translation.x;
                    const translateY = viewport.translation.y;

                    context.translate(canvasWidth / 2, canvasHeight / 2);

                    context.rotate(rotation);

                    context.scale(hflip, vflip);

                    context.scale(scale, scale);

                    context.translate(translateX, translateY);

                    context.translate(-imageWidth / 2, -imageHeight / 2);

                    context.drawImage(
                        element._offscreenCanvasNadaAerada,
                        0, 0, imageWidth, imageHeight, // Source
                        0, 0, imageWidth, imageHeight  // Destination
                    );

                    context.restore();
                };

                const onNewImage = function (event) {
                    computeMaskData();
                };

                element.addEventListener('cornerstoneimagerendered', onImageRendered);
                element.addEventListener('cornerstonenewimage', onNewImage);

                element._onImageRenderedHandlerNadaAerada = onImageRendered;
                element._onNewImageHandlerNadaAerada = onNewImage;

                cornerstone.updateImage(element);
            } else {
                maskStates['nada-aerada'] = false;

                if (element._onImageRenderedHandlerNadaAerada) {
                    element.removeEventListener('cornerstoneimagerendered', element._onImageRenderedHandlerNadaAerada);
                    delete element._onImageRenderedHandlerNadaAerada;
                }

                if (element._onNewImageHandlerNadaAerada) {
                    element.removeEventListener('cornerstonenewimage', element._onNewImageHandlerNadaAerada);
                    delete element._onNewImageHandlerNadaAerada;
                }

                delete element._offscreenCanvasNadaAerada;

                cornerstone.updateImage(element);
            }
        }

        const customHandler = (event) => {
            console.log("custom work bitch!")
            // Define o alfa como 255 (totalmente opaco);
            const customColor = hexToRGBA("#000000");
            console.log(customColor);
            const element = document.getElementById(`dicomTela${idTela}`);

            if (event.target.checked) {
                maskStates['custom-aerada'] = true;

                const limitesGerais = contornosFiltrados.contornos;

                function isPointInPolygon(x, y, polygon) {
                    let inside = false;
                    const numPoints = polygon.length;
                    let j = numPoints - 1;

                    for (let i = 0; i < numPoints; i++) {
                        const xi = polygon[i][0], yi = polygon[i][1];
                        const xj = polygon[j][0], yj = polygon[j][1];

                        const intersect =
                            (yi > y) !== (yj > y) &&
                            x < ((xj - xi) * (y - yi)) / (yj - yi + 0.0000001) + xi;

                        if (intersect) inside = !inside;
                        j = i;
                    }

                    return inside;
                }

                function getCurrentImageIndex() {
                    const image = cornerstone.getImage(element);
                    const imageId = image.imageId;
                    const imageIdIndex = arquivos.indexOf(imageId.substring(8));
                    return imageIdIndex;
                }

                function computeMaskData() {
                    const image = cornerstone.getImage(element);
                    const pixelData = image.getPixelData();
                    const rescaleSlope = image.slope;
                    const rescaleIntercept = image.intercept;

                    function toHU(pixelValue) {
                        return pixelValue * rescaleSlope + rescaleIntercept;
                    }

                    const width = image.width;
                    const height = image.height;
                    const numPixels = pixelData.length;

                    const maskData = new Uint8ClampedArray(numPixels * 4);

                    const atual = getCurrentImageIndex();
                    const polygons = limitesGerais[atual];

                    if (!Array.isArray(polygons) || polygons.length === 0) {
                        console.log('Nenhum polígono encontrado para a imagem atual.');
                        element._offscreenCanvasCustomAerada = null;
                        return;
                    }

                    let minX = Infinity,
                        minY = Infinity,
                        maxX = -Infinity,
                        maxY = -Infinity;

                    // Calcula o bounding box (retângulo delimitador)
                    polygons.forEach((polygon) => {
                        polygon.forEach((point) => {
                            const x = point[0];
                            const y = point[1];
                            if (x < minX) minX = x;
                            if (y < minY) minY = y;
                            if (x > maxX) maxX = x;
                            if (y > maxY) maxY = y;
                        });
                    });

                    minX = Math.max(0, Math.floor(minX));
                    minY = Math.max(0, Math.floor(minY));
                    maxX = Math.min(width - 1, Math.ceil(maxX));
                    maxY = Math.min(height - 1, Math.ceil(maxY));

                    for (let y = minY; y <= maxY; y++) {
                        for (let x = minX; x <= maxX; x++) {
                            const i = y * width + x;

                            let isInsideAnyPolygon = false;
                            for (let polygon of polygons) {
                                if (isPointInPolygon(x, y, polygon)) {
                                    isInsideAnyPolygon = true;
                                    break;
                                }
                            }

                            if (isInsideAnyPolygon) {
                                const huValue = toHU(pixelData[i]);
                                if (huValue >= aerationCustom?.min && huValue <= aerationCustom?.max) {
                                    // Aplica a cor escolhida
                                    maskData[i * 4] = customColor[0];     // R
                                    maskData[i * 4 + 1] = customColor[1]; // G
                                    maskData[i * 4 + 2] = customColor[2]; // B
                                    maskData[i * 4 + 3] = customColor[3]; // A
                                } else {
                                    maskData[i * 4 + 3] = 0; // Transparente
                                }
                            } else {
                                maskData[i * 4 + 3] = 0; // Transparente
                            }
                        }
                    }

                    const imageData = new ImageData(maskData, width, height);
                    const offscreenCanvas = document.createElement('canvas');
                    offscreenCanvas.width = width;
                    offscreenCanvas.height = height;
                    const offscreenCtx = offscreenCanvas.getContext('2d');
                    offscreenCtx.putImageData(imageData, 0, 0);

                    element._offscreenCanvasCustomAerada = offscreenCanvas;
                }

                const onImageRendered = function (event) {
                    if (!maskStates['custom-aerada']) return;

                    const element = event.target;
                    const context = event.detail.canvasContext;
                    const viewport = cornerstone.getViewport(element);
                    const image = cornerstone.getImage(element);

                    if (!element._offscreenCanvasCustomAerada) {
                        return;
                    }

                    context.save();
                    context.setTransform(1, 0, 0, 1, 0, 0);

                    const canvas = context.canvas;
                    const canvasWidth = canvas.width;
                    const canvasHeight = canvas.height;

                    const imageWidth = image.width;
                    const imageHeight = image.height;

                    const scale = viewport.scale;
                    const rotation = (viewport.rotation || 0) * Math.PI / 180;
                    const hflip = viewport.hflip ? -1 : 1;
                    const vflip = viewport.vflip ? -1 : 1;
                    const translateX = viewport.translation.x;
                    const translateY = viewport.translation.y;

                    context.translate(canvasWidth / 2, canvasHeight / 2);
                    context.rotate(rotation);
                    context.scale(hflip, vflip);
                    context.scale(scale, scale);
                    context.translate(translateX, translateY);
                    context.translate(-imageWidth / 2, -imageHeight / 2);

                    context.drawImage(
                        element._offscreenCanvasCustomAerada,
                        0,
                        0,
                        imageWidth,
                        imageHeight, // Source
                        0,
                        0,
                        imageWidth,
                        imageHeight // Destination
                    );

                    context.restore();
                };

                const onNewImage = function () {
                    computeMaskData();
                };

                element.addEventListener('cornerstoneimagerendered', onImageRendered);
                element.addEventListener('cornerstonenewimage', onNewImage);

                element._onImageRenderedHandlerCustomAerada = onImageRendered;
                element._onNewImageHandlerCustomAerada = onNewImage;

                computeMaskData();
                cornerstone.updateImage(element);

                console.log('Máscara custom aplicada e event listeners adicionados.');
            } else {
                maskStates['custom-aerada'] = false;

                if (element._onImageRenderedHandlerCustomAerada) {
                    element.removeEventListener(
                        'cornerstoneimagerendered',
                        element._onImageRenderedHandlerCustomAerada
                    );
                    delete element._onImageRenderedHandlerCustomAerada;
                }

                if (element._onNewImageHandlerCustomAerada) {
                    element.removeEventListener(
                        'cornerstonenewimage',
                        element._onNewImageHandlerCustomAerada
                    );
                    delete element._onNewImageHandlerCustomAerada;
                }

                delete element._offscreenCanvasCustomAerada;
                cornerstone.updateImage(element);
            }
        };

        if (hiperElement) hiperElement.addEventListener('change', hiperHandler);
        if (normalElement) normalElement.addEventListener('change', normalHandler);
        if (poucoElement) poucoElement.addEventListener('change', poucoHandler);
        if (nadaElement) nadaElement.addEventListener('change', nadaHandler);
        if (customElement) customElement.addEventListener('change', customHandler);

        // Função de limpeza para remover os listeners
        return () => {
            if (hiperElement) hiperElement.removeEventListener('change', hiperHandler);
            if (normalElement) normalElement.removeEventListener('change', normalHandler);
            if (poucoElement) poucoElement.removeEventListener('change', poucoHandler);
            if (nadaElement) nadaElement.removeEventListener('change', nadaHandler);
            if (customElement) customElement.removeEventListener('change', customHandler);
        };
    }, [contornosFiltrados, selecionada, aerationCustom?.min, aerationCustom?.max]);
}