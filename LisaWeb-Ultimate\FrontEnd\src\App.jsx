import { useState } from 'react';
import './App.css';
import Login from './routes/Login/Login.jsx';
import Register from './routes/Register/Register.jsx';
import Hero from './routes/Hero/Hero.jsx';
import <PERSON><PERSON><PERSON> from '/LisaLogo.png';

function App() {
  const [tela, setTela] = useState(1);

  return (
    <div className='app-main'>
      <div className='tela-entrada'>
        <div className="logo-container" onClick={() => setTela(3)}>
          <img src={LisaLogo} alt="Lisa Logo" className="logo" />
        </div>

        <div className="abas-container">
          <p
            className={tela === 1 ? 'tab active' : 'tab'}
            onClick={() => setTela(1)}
          >
            Login
          </p>
          <p
            className={tela === 2 ? 'tab active' : 'tab'}
            onClick={() => setTela(2)}
          >
            Registrar-se
          </p>
        </div>
      </div>


      {tela === 1 ? (
        <Login />
      ) : tela === 2 ? (
        <Register setTela={setTela} />
      ) : tela === 3 ? (
        <Hero />
      ) : null}

      <div className="logos-bottom-left">
        <a
          href="https://www.lesc-lab.com.br/"
          target="_blank"
          rel="noopener noreferrer"
        >
          <img src="/Simbolo-lesc.png" alt="LESC" className="logo" />
        </a>
        <div className="separator"></div>
        <a
          href="https://www.ufc.br/"
          target="_blank"
          rel="noopener noreferrer"
        >
          <img src="/Simbolo-ufc.png" alt="UFC" className="logo" />
        </a>
      </div>
    </div>
  );
}

export default App;
